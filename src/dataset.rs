use candle_core::{<PERSON><PERSON>, Result, Tensor};
use polars::prelude::*;

/// Loads data from a Parquet file, imputes missing values, calculates returns,
/// and converts it to a Candle tensor.
pub fn load_and_prepare_data(
    path: &str,
    device: &Device,
) -> Result<Tensor> {
    let df_lazy = LazyFrame::scan_parquet(path, Default::default())
        .expect("Failed to scan Parquet file");

    // Convert the clean Polars DataFrame to a Candle Tensor.
    // First collect the LazyFrame to get a DataFrame
    let df = df_lazy
        .collect()
        .expect("Failed to collect LazyFrame");

    // Extract numeric columns and convert to Vec<Vec<f32>>
    let mut data: Vec<f32> = Vec::new();
    let mut shape = vec![df.height()];

    // Get all numeric columns
    let numeric_columns: Vec<_> = df.get_columns()
        .iter()
        .filter(|col| {
            matches!(col.dtype(),
                DataType::Float64 | DataType::Float32 |
                DataType::Int64 | DataType::Int32 |
                DataType::UInt64 | DataType::UInt32
            )
        })
        .collect();

    if numeric_columns.is_empty() {
        return Err(candle_core::Error::Msg("No numeric columns found in DataFrame".to_string()));
    }

    shape.push(numeric_columns.len());

    // Extract data column by column
    for column in numeric_columns {
        match column.dtype() {
            DataType::Float64 => {
                let series = column.f64().expect("Failed to cast to f64");
                for value in series.iter() {
                    data.push(value.unwrap_or(0.0) as f32);
                }
            }
            DataType::Float32 => {
                let series = column.f32().expect("Failed to cast to f32");
                for value in series.iter() {
                    data.push(value.unwrap_or(0.0));
                }
            }
            DataType::Int64 => {
                let series = column.i64().expect("Failed to cast to i64");
                for value in series.iter() {
                    data.push(value.unwrap_or(0) as f32);
                }
            }
            DataType::Int32 => {
                let series = column.i32().expect("Failed to cast to i32");
                for value in series.iter() {
                    data.push(value.unwrap_or(0) as f32);
                }
            }
            _ => {
                return Err(candle_core::Error::Msg(format!("Unsupported numeric type: {:?}", column.dtype())));
            }
        }
    }

    let returns_tensor = Tensor::from_vec(data, shape, device)?;

    println!("Data loaded and prepared. Shape: {:?}", returns_tensor.shape());
    Ok(returns_tensor)
}
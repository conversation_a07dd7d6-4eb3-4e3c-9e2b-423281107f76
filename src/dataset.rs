use candle_core::{<PERSON><PERSON>, Result, Tensor};
use polars::prelude::*;

/// Loads data from a Parquet file, imputes missing values, calculates returns,
/// and converts it to a Candle tensor.
pub fn load_and_prepare_data(
    path: &str,
    device: &Device,
) -> Result<Tensor> {
    let df_lazy = LazyFrame::scan_parquet(path, Default::default())
        .expect("Failed to scan Parquet file");

    // Convert the clean Polars DataFrame to a Candle Tensor.
    let returns_ndarray = df_lazy
        .to_ndarray::<Float64Type>()
        .expect("Failed to convert to ndarray");

    let returns_tensor = Tensor::from_ndarray(
        returns_ndarray.mapv(|x| x as f32) // Cast to F32 for the model
    ).to_device(device)?;

    println!("Data loaded and prepared. Shape: {:?}", returns_tensor.shape());
    Ok(returns_tensor)
}